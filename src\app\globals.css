@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

:root {
  --radius: 0.625rem;
  /* EY Color Palette */
  --ey-yellow: #FFE600;
  --ey-yellow-alt: #FFDB00;
  --ey-black: #333333;
  --ey-off-black: #161D23;
  --white: #FFFFFF;
  
  /* Main theme colors using EY palette */
  --background: var(--white);
  --foreground: var(--ey-off-black);
  --card: var(--white);
  --card-foreground: var(--ey-off-black);
  --popover: var(--white);
  --popover-foreground: var(--ey-off-black);
  --primary: var(--ey-yellow);
  --primary-foreground: var(--ey-off-black);
  --secondary: var(--ey-black);
  --secondary-foreground: var(--white);
  --muted: #F5F5F5;
  --muted-foreground: var(--ey-black);
  --accent: var(--ey-yellow-alt);
  --accent-foreground: var(--ey-off-black);
  --destructive: #FF3B30;
  --border: var(--ey-black);
  --input: var(--white);
  --ring: var(--ey-yellow);
  
  /* Chart colors using EY palette */
  --chart-1: var(--ey-yellow);
  --chart-2: var(--ey-black);
  --chart-3: var(--ey-yellow-alt);
  --chart-4: var(--ey-off-black);
  --chart-5: #F5F5F5;
  
  /* Sidebar colors using EY palette */
  --sidebar: var(--ey-off-black);
  --sidebar-foreground: var(--white);
  --sidebar-primary: var(--ey-yellow);
  --sidebar-primary-foreground: var(--ey-off-black);
  --sidebar-accent: var(--ey-yellow-alt);
  --sidebar-accent-foreground: var(--ey-off-black);
  --sidebar-border: var(--ey-black);
  --sidebar-ring: var(--ey-yellow);
}

.dark {
  /* Dark theme using EY palette */
  --background: var(--ey-off-black);
  --foreground: var(--white);
  --card: var(--ey-black);
  --card-foreground: var(--white);
  --popover: var(--ey-black);
  --popover-foreground: var(--white);
  --primary: var(--ey-yellow);
  --primary-foreground: var(--ey-off-black);
  --secondary: var(--ey-yellow-alt);
  --secondary-foreground: var(--ey-off-black);
  --muted: var(--ey-black);
  --muted-foreground: var(--ey-yellow);
  --accent: var(--ey-yellow-alt);
  --accent-foreground: var(--ey-off-black);
  --destructive: #FF453A;
  --border: var(--ey-yellow);
  --input: rgba(255, 255, 255, 0.15);
  --ring: var(--ey-yellow);
  
  /* Chart colors for dark mode */
  --chart-1: var(--light-sea-green);
  --chart-2: var(--dodger-blue);
  --chart-3: var(--ultra-violet);
  --chart-4: #C4B5FD;
  --chart-5: #FBBF24;
  
  /* Sidebar colors for dark mode */
  --sidebar: var(--black);
  --sidebar-foreground: var(--white);
  --sidebar-primary: var(--light-sea-green);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: var(--dodger-blue);
  --sidebar-accent-foreground: var(--white);
  --sidebar-border: rgba(252, 252, 252, 0.1);
  --sidebar-ring: var(--light-sea-green);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
