"use client";

import React from "react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { 
  TrendingUp, 
  Shield, 
  Settings 
} from "lucide-react";

interface ActionButtonsProps {
  dict: any;
  lang: string;
}

interface ActionButton {
  title: string;
  icon: React.ElementType;
  href: string;
  colorClass: string;
  hoverColorClass: string;
}

export default function ActionButtons({ dict, lang }: ActionButtonsProps) {
  const actionButtons: ActionButton[] = [
    {
      title: dict.homepage.actions.ndiMaturity,
      icon: TrendingUp,
      href: `/${lang}/ndi-maturity`,
      colorClass: "bg-ey-yellow text-ey-off-black border-ey-yellow",
      hoverColorClass: "hover:bg-ey-yellow-alt hover:border-ey-yellow-alt"
    },
    {
      title: dict.homepage.actions.ndiCompliance,
      icon: Shield,
      href: `/${lang}/ndi-compliance`,
      colorClass: "bg-ey-black text-white border-ey-black",
      hoverColorClass: "hover:bg-ey-off-black hover:border-ey-off-black"
    },
    {
      title: dict.homepage.actions.ndiOperationalExcellence,
      icon: Settings,
      href: `/${lang}/ndi-operational-excellence`,
      colorClass: "bg-ey-yellow-alt text-ey-off-black border-ey-yellow-alt",
      hoverColorClass: "hover:bg-ey-yellow hover:border-ey-yellow"
    }
  ];

  return (
    <div className="container mx-auto px-6 py-16">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {actionButtons.map((button, index) => {
          const Icon = button.icon;
          
          return (
            <Link
              key={index}
              href={button.href}
              className={cn(
                "group relative overflow-hidden rounded-2xl border-2 p-8 transition-all duration-300 ease-in-out transform hover:scale-105 hover:shadow-2xl",
                button.colorClass,
                button.hoverColorClass
              )}
            >
              {/* Background gradient effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              
              {/* Content */}
              <div className="relative z-10 text-center">
                <div className="mb-6 flex justify-center">
                  <div className="rounded-full bg-white/20 p-4 group-hover:bg-white/30 transition-colors duration-300">
                    <Icon className="h-8 w-8" />
                  </div>
                </div>
                
                <h3 className="text-xl font-bold mb-2 group-hover:scale-105 transition-transform duration-300">
                  {button.title}
                </h3>
                
                {/* Subtle arrow indicator */}
                <div className="mt-4 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="h-0.5 w-8 bg-current rounded-full" />
                </div>
              </div>
              
              {/* Hover effect border */}
              <div className="absolute inset-0 rounded-2xl border-2 border-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </Link>
          );
        })}
      </div>
    </div>
  );
}
