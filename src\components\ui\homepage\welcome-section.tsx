"use client";

import React from "react";

interface WelcomeSectionProps {
  dict: any;
}

export default function WelcomeSection({ dict }: WelcomeSectionProps) {
  return (
    <div className="bg-white border-b border-border">
      <div className="container mx-auto px-6 py-12">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-ey-off-black mb-4 lg:text-5xl">
            {dict.homepage.welcome.title}
          </h1>
          <p className="text-lg text-ey-black/70 max-w-2xl mx-auto">
            {dict.homepage.welcome.subtitle}
          </p>
        </div>
      </div>
    </div>
  );
}
