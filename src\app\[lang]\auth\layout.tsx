import React from "react";
import { Locale } from "@/i18n-config";
import { getDictionary } from "@/dictionaries";
import Image from "next/image";
import { Toaster } from "@/components/ui/toaster";
import LanguageSwitcher from "@/components/ui/language-switcher";

export default async function AuthLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ lang: Locale }>;
}) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Language Switcher */}
      <div className="absolute top-4 right-4 z-10">
        <LanguageSwitcher />
      </div>
      {/* Main content */}
      <main className="flex-1 flex flex-col items-center justify-center p-4 pt-12 md:p-8 md:pt-16">
        <div className="w-full max-w-md mb-8 flex justify-center">
          <Image
            src="/next.svg"
            alt={dict.layout.title}
            width={120}
            height={30}
            priority
            className="dark:invert"
          />
        </div>
        {children}
      </main>

      {/* Footer */}
      <footer className="py-6 text-center text-sm text-muted-foreground">
        <p>© {new Date().getFullYear()} {dict.layout.title}</p>
      </footer>

      {/* Toast notifications */}
      <Toaster />
    </div>
  );
}
