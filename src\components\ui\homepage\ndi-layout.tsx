"use client";

import React, { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { 
  Home, 
  FileText, 
  Settings,
  Menu,
  X
} from "lucide-react";
import { Button } from "@/components/ui/button";

interface NDILayoutProps {
  children: React.ReactNode;
  dict: any;
  lang: string;
}

interface NavItem {
  name: string;
  icon: React.ElementType;
  path: string;
  key: keyof typeof dict.homepage.navigation;
}

export default function NDILayout({ children, dict, lang }: NDILayoutProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  const navItems: NavItem[] = [
    {
      name: dict.homepage.navigation.home,
      icon: Home,
      path: `/${lang}`,
      key: "home"
    },
    {
      name: dict.homepage.navigation.ndiDocuments,
      icon: FileText,
      path: `/${lang}/ndi-documents`,
      key: "ndiDocuments"
    },
    {
      name: dict.homepage.navigation.projectTools,
      icon: Settings,
      path: `/${lang}/project-tools`,
      key: "projectTools"
    }
  ];

  const isActive = (path: string) => {
    if (path === `/${lang}`) {
      return pathname === path;
    }
    return pathname.startsWith(path);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="bg-background/80 backdrop-blur-sm"
        >
          {isMobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed left-0 top-0 z-40 h-screen w-80 transform bg-ey-off-black text-white transition-transform duration-300 ease-in-out lg:translate-x-0",
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
      >
        {/* Sidebar Header */}
        <div className="flex h-20 items-center justify-center border-b border-ey-black/20 px-6">
          <div className="text-center">
            <h1 className="text-xl font-bold text-white">
              {dict.homepage.title}
            </h1>
            <p className="text-sm text-white/70">
              {dict.homepage.subtitle}
            </p>
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 px-4 py-6">
          <ul className="space-y-2">
            {navItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <li key={item.key}>
                  <Link
                    href={item.path}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={cn(
                      "flex items-center gap-3 rounded-lg px-4 py-3 text-sm font-medium transition-all duration-200 hover:bg-ey-yellow hover:text-ey-off-black",
                      active
                        ? "bg-ey-yellow text-ey-off-black shadow-sm"
                        : "text-white/90 hover:text-ey-off-black"
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{item.name}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>
      </aside>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Main Content */}
      <main className="lg:ml-80">
        <div className="min-h-screen bg-background">
          {children}
        </div>
      </main>
    </div>
  );
}
