"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AuthCard } from "@/components/ui/authUI/AuthCard";
import { Button } from "@/components/ui/button";
import { auth, sendUserEmailVerification, logoutUser } from "@/Firebase/Authentication/authConfig";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";

export default function VerifyClient({
  dict
}: {
  dict: Dictionary
}) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    // Check if user is logged in
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        setEmail(user.email);

        // If email is already verified, redirect to homepage
        if (user.emailVerified) {
          router.push("/");
        }
      } else {
        // If no user is logged in, redirect to sign in
        router.push("signin");
      }
    });

    return () => unsubscribe();
  }, [router]);

  useEffect(() => {
    // Countdown for resend button
    if (countdown > 0 && !canResend) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
  }, [countdown, canResend]);

  const handleResendEmail = async () => {
    try {
      setIsLoading(true);
      await sendUserEmailVerification();
      toast({
        title: dict.auth.verify.success,
        variant: "default",
      });
      setCanResend(false);
      setCountdown(60);
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.verify.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await logoutUser();
      router.push("signin");
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: "Error signing out",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <AuthCard
      title={dict.auth.verify.title}
      description={dict.auth.verify.subtitle}
    >
      <div className="space-y-6">
        {email && (
          <div className="bg-muted p-4 rounded-md text-center">
            <p className="text-sm font-medium">{email}</p>
          </div>
        )}

        <p className="text-sm text-center text-muted-foreground">
          {dict.auth.verify.checkInbox}
        </p>

        <div className="space-y-2">
          <Button
            onClick={handleResendEmail}
            variant="outline"
            className="w-full"
            disabled={isLoading || !canResend}
          >
            {isLoading ? (
              <span className="flex items-center gap-2">
                <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                {dict.auth.verify.resendEmail}
              </span>
            ) : canResend ? (
              dict.auth.verify.resendEmail
            ) : (
              `${dict.auth.verify.resendEmail} (${countdown}s)`
            )}
          </Button>

          <Button
            onClick={() => {
              try {
                // Reload the user to check if email is verified
                auth.currentUser?.reload().then(() => {
                  if (auth.currentUser?.emailVerified) {
                    // If email is verified, redirect to success page
                    router.push("success");
                  } else {
                    // Otherwise, just reload the page
                    window.location.reload();
                  }
                }).catch(error => {
                  const errorMessage = handleAuthError(error);
                  toast({
                    title: dict.auth.verify.error,
                    description: errorMessage,
                    variant: "destructive",
                  });
                });
              } catch (error) {
                const errorMessage = handleAuthError(error);
                toast({
                  title: dict.auth.verify.error,
                  description: errorMessage,
                  variant: "destructive",
                });
              }
            }}
            className="w-full"
          >
            {dict.auth.verify.button}
          </Button>
        </div>

        <div className="text-center">
          <button
            onClick={handleSignOut}
            className="text-sm text-muted-foreground hover:text-foreground"
          >
            {dict.auth.reset.backToSignIn}
          </button>
        </div>
      </div>
    </AuthCard>
  );
}
