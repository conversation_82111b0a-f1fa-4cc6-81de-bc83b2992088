import { getDictionary } from "../../dictionaries";
import type { Locale } from "../../i18n-config";
import { NDILayout, WelcomeSection, ActionButtons, ProtectedRoute } from "@/components/ui/homepage";

export default async function Home({ params }: { params: Promise<{ lang: Locale }> }) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <ProtectedRoute lang={lang}>
      <NDILayout dict={dict} lang={lang}>
        <div className="min-h-screen bg-background">
          {/* Welcome Section */}
          <WelcomeSection dict={dict} />

          {/* Main Content Area */}
          <div className="bg-white">
            {/* Action Buttons */}
            <ActionButtons dict={dict} lang={lang} />
          </div>
        </div>
      </NDILayout>
    </ProtectedRoute>
  );
}