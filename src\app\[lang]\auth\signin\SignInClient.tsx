"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { z } from "zod";
import { AuthCard } from "@/components/ui/authUI/AuthCard";
import { AuthForm } from "@/components/ui/authUI/AuthForm";
import { FormField } from "@/components/ui/authUI/FormField";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { useAuth } from "@/context";

export default function SignInClient({
  dict
}: {
  dict: Dictionary
}) {
  const router = useRouter();
  const { toast } = useToast();
  const { login, loginWithGoogle, loading: authLoading, error: authError } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  // Form schema
  const formSchema = z.object({
    email: z.string().email({
      message: dict.auth.common.invalidEmail,
    }),
    password: z.string().min(1, {
      message: dict.auth.common.requiredField,
    }),
    rememberMe: z.boolean().optional(),
  });

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      // Sign in with email and password using our context
      await login(data.email, data.password);

      toast({
        title: dict.auth.signin.success,
        variant: "default",
      });
      router.push("success");
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.signin.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      // Sign in with Google using our context
      await loginWithGoogle();

      toast({
        title: dict.auth.signin.success,
        variant: "default",
      });
      router.push("success");
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.signin.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthCard
      title={dict.auth.signin.title}
      description={dict.auth.signin.subtitle}
      footer={
        <div className="text-center mt-4">
          <p className="text-sm text-muted-foreground">
            {dict.auth.common.dontHaveAccount}{" "}
            <Link
              href="signup"
              className="text-primary underline underline-offset-4 hover:text-primary/90"
            >
              {dict.auth.signup.title}
            </Link>
          </p>
        </div>
      }
    >
      <AuthForm
        schema={formSchema}
        onSubmit={onSubmit}
        submitText={dict.auth.signin.button}
        isLoading={isLoading || authLoading}
        googleSignIn={handleGoogleSignIn}
        googleText={dict.auth.common.continueWithGoogle}
      >
        <FormField
          name="email"
          label={dict.auth.common.email}
          placeholder="<EMAIL>"
          type="email"
          required
          autoComplete="email"
        />
        <FormField
          name="password"
          label={dict.auth.common.password}
          placeholder="••••••••"
          type="password"
          required
          autoComplete="current-password"
        />
        <div className="flex items-center justify-between">
          <FormField
            name="rememberMe"
            isCheckbox
            checkboxLabel={dict.auth.common.rememberMe}
          />
          <Link
            href="reset"
            className="text-sm font-medium text-primary hover:underline"
          >
            {dict.auth.common.forgotPassword}
          </Link>
        </div>
      </AuthForm>
    </AuthCard>
  );
}
