"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";


// Icons
import {
  Home,
  Settings,
  LogOut,
  ChevronLeft,
  ChevronRight,
  MoonStar,
  Sun,
  User,
  Lightbulb,
  Boxes
} from "lucide-react";

interface NavItem {
  name: string;
  icon: React.ElementType;
  path: string;
}

// Navigation configuration
const NAV_ITEMS: NavItem[] = [
  { 
    name: 'Home', 
    icon: Home,
    path: '/StrategyDashboard'
  },
  { 
    name: 'My Strategy', 
    icon: Lightbulb,
    path: '/StrategyDashboard/my-strategy'
  },
  { 
    name: 'Settings', 
    icon: Settings,
    path: '/StrategyDashboard/settings'
  }
];

export default function StrategyDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [currentPage, setCurrentPage] = useState("Home");
  const [isDarkMode, setIsDarkMode] = useState(false);
  const { user, loading, logout } = useAuth();
  const router = useRouter();
  
  // Initialize theme from localStorage if available
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      setIsDarkMode(true);
      document.documentElement.classList.add('dark');
    }
  }, []);

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin');
    }
  }, [user, loading, router]);

  // Toggle theme function
  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    if (isDarkMode) {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    } else {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    }
  };

  // Handle navigation
  const handleNavigation = (item: NavItem) => {
    setCurrentPage(item.name);
    router.push(item.path);
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed', error);
    }
  };

  // Define theme-based styles
  const sidebarBackground = isDarkMode 
    ? "bg-gradient-to-b from-teal-800 via-teal-900 to-slate-900" 
    : "bg-gradient-to-b from-[#00785d] to-teal-600";
  
  // Active tab background and indicator colors
  const activeTabBg = isDarkMode 
    ? 'bg-white/20' 
    : 'bg-white/25';
  
  const activeIndicatorColor = isDarkMode 
    ? 'bg-white' 
    : 'bg-white';

  const hoverBg = isDarkMode 
    ? 'hover:bg-white/10' 
    : 'hover:bg-white/15';

  if (loading) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <motion.div
        initial={{ width: '280px' }}
        animate={{ width: isCollapsed ? '80px' : '280px' }}
        transition={{ duration: 0.3 }}
        className={cn(
          "h-screen text-white flex flex-col justify-between sticky top-0",
          sidebarBackground
        )}
      >
        {/* Top Section with Profile */}
        <div>
          {/* User Profile Section */}
          <div className="pt-6 pb-4 px-4">
            <div className={cn(
              "flex items-center gap-4 mb-4",
              isCollapsed ? "justify-center" : "justify-start"
            )}>
              <div className={cn(
                "relative group",
                isCollapsed && "mx-auto"
              )}>
                <div className="w-12 h-12 rounded-full bg-white/10 backdrop-blur-sm border-2 border-white/30 overflow-hidden flex items-center justify-center shadow-lg">
                  {user?.photoURL ? (
                    <div className="w-full h-full relative">
                      <div 
                        className="w-full h-full" 
                        style={{
                          backgroundImage: `url(${user.photoURL})`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center'
                        }}
                      />
                    </div>
                  ) : (
                    <User className="w-6 h-6 text-white/80" />
                  )}
                </div>
                <div className="absolute inset-0 bg-white/0 group-hover:bg-white/10 rounded-full transition-all" />
              </div>

              {!isCollapsed && (
                <motion.div 
                  initial={{ opacity: 1 }}
                  animate={{ opacity: isCollapsed ? 0 : 1 }}
                  transition={{ duration: 0.2 }}
                  className="flex-1 overflow-hidden"
                >
                  <h3 className="font-bold text-lg truncate">
                    {user?.displayName || user?.email?.split('@')[0] || 'User'}
                  </h3>
                  <p className="text-white/70 text-sm truncate">
                    {user?.email}
                  </p>
                </motion.div>
              )}
            </div>

            {/* Collapsible separator */}
            <div className={cn(
              "h-px bg-white/20 mx-auto my-4",
              isCollapsed ? "w-8" : "w-full"
            )} />
          </div>

          {/* Navigation Items */}
          <nav className="p-3 space-y-1.5 mt-2">
            {NAV_ITEMS.map((item) => (
              <motion.button
                key={item.name}
                onClick={() => handleNavigation(item)}
                className={cn(
                  "w-full flex items-center rounded-lg transition-colors relative",
                  currentPage === item.name
                    ? activeTabBg
                    : hoverBg,
                  isCollapsed 
                    ? "justify-center py-3 px-2" 
                    : "px-4 py-3"
                )}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className={cn(
                  "flex items-center justify-center w-7 h-7",
                  !isCollapsed && "mr-3"
                )}>
                  <item.icon className="w-5 h-5 text-white" />
                </div>
                {!isCollapsed && (
                  <motion.span
                    initial={{ opacity: 1 }}
                    animate={{ opacity: isCollapsed ? 0 : 1 }}
                    transition={{ duration: 0.2 }}
                    className="whitespace-nowrap font-medium text-left"
                  >
                    {item.name}
                  </motion.span>
                )}
                {currentPage === item.name && !isCollapsed && (
                  <motion.div
                    className={cn(
                      "absolute rounded-sm w-1 h-full right-0",
                      activeIndicatorColor
                    )}
                    layoutId="activeTab"
                    transition={{ type: "spring", stiffness: 300, damping: 30 }}
                  />
                )}
              </motion.button>
            ))}
          </nav>
        </div>

        {/* Bottom Section */}
        <div className="p-3 border-t border-white/20 space-y-2">
          {/* Module Selection Button - Special Styling */}
          <motion.button
            onClick={() => router.push('/module-selection')}
            className={cn(
              "w-full flex items-center rounded-xl transition-all relative overflow-hidden mb-2",
              isCollapsed ? "justify-center py-4 px-2" : "px-5 py-4",
              isDarkMode
                ? "bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700"
                : "bg-gradient-to-r from-[#00785d] to-teal-600 hover:from-[#00916f] hover:to-teal-500"
            )}
            whileHover={{ scale: 1.03, y: -2 }}
            whileTap={{ scale: 0.98 }}
          >
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-16 h-16 rounded-full opacity-20 bg-white transform translate-x-1/3 -translate-y-1/3"></div>
            <div className="absolute bottom-0 left-0 w-12 h-12 rounded-full opacity-10 bg-white transform -translate-x-1/3 translate-y-1/3"></div>
            
            <div className={cn(
              "flex items-center justify-center w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg",
              !isCollapsed && "mr-3"
            )}>
              <Boxes className="w-5 h-5 text-white" />
            </div>
            
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 1 }}
                animate={{ opacity: isCollapsed ? 0 : 1 }}
                transition={{ duration: 0.2 }}
                className="flex-1"
              >
                <span className="whitespace-nowrap font-medium text-white text-left">
                  Module Selection
                </span>
                <p className="text-xs text-white/80 mt-0.5">
                  Switch between app modules
                </p>
              </motion.div>
            )}
            
            {!isCollapsed && (
              <ChevronRight className="w-4 h-4 text-white/80 ml-1" />
            )}
          </motion.button>

          {/* Theme Toggle */}
          <motion.button
            onClick={toggleTheme}
            className={cn(
              "w-full flex items-center rounded-lg transition-colors",
              hoverBg,
              isCollapsed 
                ? "justify-center py-3 px-2" 
                : "px-4 py-3"
            )}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className={cn(
              "flex items-center justify-center w-7 h-7",
              !isCollapsed && "mr-3"
            )}>
              {isDarkMode ? (
                <Sun className="w-5 h-5 text-white" />
              ) : (
                <MoonStar className="w-5 h-5 text-white" />
              )}
            </div>
            {!isCollapsed && (
              <motion.span
                initial={{ opacity: 1 }}
                animate={{ opacity: isCollapsed ? 0 : 1 }}
                transition={{ duration: 0.2 }}
                className="whitespace-nowrap font-medium text-left"
              >
                {isDarkMode ? 'Light Mode' : 'Dark Mode'}
              </motion.span>
            )}
          </motion.button>

          {/* Logout Button */}
          <motion.button
            onClick={handleLogout}
            className={cn(
              "w-full flex items-center rounded-lg transition-colors",
              hoverBg,
              isCollapsed 
                ? "justify-center py-3 px-2" 
                : "px-4 py-3"
            )}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className={cn(
              "flex items-center justify-center w-7 h-7",
              !isCollapsed && "mr-3"
            )}>
              <LogOut className="w-5 h-5 text-white" />
            </div>
            {!isCollapsed && (
              <motion.span
                initial={{ opacity: 1 }}
                animate={{ opacity: isCollapsed ? 0 : 1 }}
                transition={{ duration: 0.2 }}
                className="whitespace-nowrap font-medium text-left"
              >
                Logout
              </motion.span>
            )}
          </motion.button>

          {/* Collapse Button */}
          <motion.button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className={cn("w-full flex items-center justify-center p-2 rounded-lg", hoverBg)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {isCollapsed ? (
              <ChevronRight className="w-5 h-5" />
            ) : (
              <ChevronLeft className="w-5 h-5" />
            )}
          </motion.button>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="flex-1">
        {children}
      </div>
    </div>
  );
} 